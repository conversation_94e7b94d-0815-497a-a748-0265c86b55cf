<script lang="ts">
	// Svelte imports
	import { createEventDispatcher, onMount, onDestroy } from 'svelte';

	// SvelteKit imports
	import { page } from '$app/stores';

	// UI component imports
	import {
		Badge,
		Table,
		TableBody,
		TableBodyCell,
		TableBodyRow,
		TableHead,
		TableHeadCell,
		Button,
		Indicator,
		Tooltip
	} from 'flowbite-svelte';
	import {
		TicketOutline,
		EditSolid,
		EditOutline,
		CloseOutline,
		UserCircleSolid,
		BriefcaseSolid
	} from 'flowbite-svelte-icons';

	// Store imports
	import { t } from '$lib/stores/i18n';

	// Utility imports
	import {
		displayDate,
		timeAgo,
		getStatusClass,
		getPriorityClass,
		getSentimentClass,
		getSentimentIcon,
		getColorClass,
		formatTime
	} from '$lib/utils';

	// API imports
	import { services } from '$lib/api/features';

	// Component imports
	import LoadingSpinner from '$lib/components/common/LoadingSpinner.svelte';
	import UserEdit from '$lib/components/UI/UserEdit.svelte';
	import UserDelete from '$lib/components/UI/UserDelete.svelte';
	import UserReactivate from '$lib/components/UI/UserReactivate.svelte';
	import UserAssignPartner from '$lib/components/UI/UserAssignPartner.svelte';
	import UserAssignRole from '$lib/components/UI/UserAssignRole.svelte';
	import UserAssignDepartment from '$lib/components/UI/UserAssignDepartment.svelte';
	import UserAssignTag from '$lib/components/UI/UserAssignTag.svelte';

	// Constants
	const ROLE_PRIORITY: Record<string, number> = {
		'System': 0,
		'Admin': -1,
		'Supervisor': -2,
		'Agent': -3
	};

	const STATUS_CONFIG = {
		online: { color: 'green', label: 'Online' },
		offline: { color: 'red', label: 'Offline' },
		away: { color: 'yellow', label: 'Away' }
	};

	const TABS = [
		{ id: 'profile', label: 'Profile', key: 'user_profile' },
		{ id: 'work', label: 'Work Info', key: 'work_information' },
		{ id: 'tickets', label: 'Tickets', key: 'my_tasks' }
	];

	// Props
	export let isOpen = false;
	export let selectedUserId: number | null = null;
	export let token: string;

	// Current user role
	$: currentUserRole = $page.data.role;

	// Permission checks
	$: isAdmin = (currentUserRole === 'Admin');
	$: isSupervisor = (currentUserRole === 'Supervisor');
	$: canEditUser = displayUser && (isAdmin || isSupervisor) && (ROLE_PRIORITY[currentUserRole] > ROLE_PRIORITY[displayUser?.roles]);

	// Event dispatcher -- used to notify parent component (table) of user updates
	const dispatch = createEventDispatcher<{
		closeSidebar: void;
		userUpdated: { userId: number };
	}>();

	// Component state
	let isLoading = false;
	let error: string | null = null;
	let user: any = null;
	let userTickets: any[] = [];
	let partners: any[] = [];
	let departments: any[] = [];
	let tags: any[] = [];
	let roles: any[] = [];
	
	// Granular loading states
	let isLoadingProfile = false;
	let isLoadingTickets = false;
	let isLoadingWorkData = false;
	
	// UI state preservation
	let savedActiveTab = 'profile';
	let savedScrollPosition = 0;

	// Dropdown state management
	let editUserDropdownOpen = false;
	let editWorkDropdownOpen = false;

	// References to dropdown elements
	let editUserDropdownElement: HTMLElement;
	let editWorkDropdownElement: HTMLElement;

	// Tab state
	let activeTab = 'profile';

	// Reactive data with proper display logic
	$: displayUser = user;
	
	// Computed properties
	$: userStatusInfo = displayUser ? (STATUS_CONFIG[displayUser.status as keyof typeof STATUS_CONFIG] || {color: 'gray', label: 'Unknown'}) : null;
	$: hasUserImage = displayUser && displayUser.image_url && displayUser.image_url.length > 0;

	// Watch for selectedUserId changes
	$: if (selectedUserId && isOpen) {
		fetchUserDetails();
	}

	// Focus management
	$: if (isOpen) {
		setTimeout(() => {
			const closeButton = document.getElementById('user-detail-sidebar-close-button');
			if (closeButton) {
				closeButton.focus();
			}
		}, 100);
	}

	// Functions
	function closeSidebar() {
		dispatch('closeSidebar');
	}
	
	// UI state preservation functions
	function saveUIState() {
		savedActiveTab = activeTab;
		const tabContent = document.getElementById('user-detail-sidebar-tab-content');
		if (tabContent) {
			savedScrollPosition = tabContent.scrollTop;
		}
	}
	
	function restoreUIState() {
		activeTab = savedActiveTab;
		setTimeout(() => {
			const tabContent = document.getElementById('user-detail-sidebar-tab-content');
			if (tabContent) {
				tabContent.scrollTop = savedScrollPosition;
			}
		}, 50);
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape') {
			// Close dropdowns first, then sidebar if no dropdowns are open
			if (editUserDropdownOpen || editWorkDropdownOpen) {
				closeAllDropdowns();
			} else {
				closeSidebar();
			}
		}
	}

	async function fetchUserDetails() {
		if (!selectedUserId || !token) return;

		isLoading = true;
		error = null;

		try {
			// Fetch user details
			const userResponse = await services.users.getById(selectedUserId.toString(), token);
			if (userResponse.res_status === 200) {
				user = userResponse.users;
			} else {
				throw new Error(userResponse.error_msg || 'Failed to fetch user details');
			}

			// Fetch user tickets
			const ticketsResponse = await services.users.getUserTickets(selectedUserId.toString(), token);
			if (ticketsResponse.res_status === 200) {
				userTickets = Array.isArray(ticketsResponse.users) ? ticketsResponse.users : (ticketsResponse.users?.tickets || []);
			}

			// Fetch related data for editing functionality
			const [partnersResponse, departmentsResponse, tagsResponse, rolesResponse] = await Promise.all([
				services.companies.getAll(token),
				services.departments.getAll(token),
				services.users.getAllTags(token),
				services.roles.getAll(token)
			]);

			partners = partnersResponse.partners || [];
			departments = Array.isArray(departmentsResponse.departments) ? departmentsResponse.departments : [];
			tags = tagsResponse.tags || [];
			roles = rolesResponse.roles || [];

		} catch (err) {
			console.error('Error fetching user details:', err);
			error = err instanceof Error ? err.message : 'Failed to load user details';
		} finally {
			isLoading = false;
			// Data has been refreshed
		}
	}
	
	// Unified refresh function for better maintainability
	async function refreshData(includeUser = false, includeTickets = false, includeWork = false) {
		if (!selectedUserId || !token) return;
		
		saveUIState();
		
		// Set loading states
		if (includeUser) isLoadingProfile = true;
		if (includeTickets) isLoadingTickets = true;
		if (includeWork) isLoadingWorkData = true;
		
		try {
			const requests = [];
			
			// Add user request if needed
			if (includeUser) {
				requests.push(services.users.getById(selectedUserId.toString(), token));
			}
			
			// Add tickets request if needed
			if (includeTickets) {
				requests.push(services.users.getUserTickets(selectedUserId.toString(), token));
			}
			
			// Add work data requests if needed
			if (includeWork) {
				requests.push(
					services.companies.getAll(token),
					services.departments.getAll(token),
					services.users.getAllTags(token),
					services.roles.getAll(token)
				);
			}
			
			const responses = await Promise.all(requests);
			let responseIndex = 0;
			
			// Process user response
			if (includeUser) {
				const userResponse = responses[responseIndex++];
				if (userResponse.res_status === 200) {
					user = userResponse.users;
				} else {
					throw new Error(userResponse.error_msg || 'Failed to refresh user data');
				}
			}
			
			// Process tickets response
			if (includeTickets) {
				const ticketsResponse = responses[responseIndex++];
				if (ticketsResponse.res_status === 200) {
					userTickets = Array.isArray(ticketsResponse.users) ? ticketsResponse.users : (ticketsResponse.users?.tickets || []);
				}
			}
			
			// Process work data responses
			if (includeWork) {
				partners = responses[responseIndex++].partners || [];
				departments = Array.isArray(responses[responseIndex].departments) ? responses[responseIndex++].departments : [];
				tags = responses[responseIndex++].tags || [];
				roles = responses[responseIndex++].roles || [];
			}
			
		} catch (err) {
			console.error('Error refreshing data:', err);
			error = err instanceof Error ? err.message : 'Failed to refresh data';
		} finally {
			if (includeUser) isLoadingProfile = false;
			if (includeTickets) isLoadingTickets = false;
			if (includeWork) isLoadingWorkData = false;
			restoreUIState();
		}
	}
	
	// Convenience functions using the unified refresh
	async function refreshUserProfile() {
		await refreshData(true, false, false);
	}
	
	async function refreshUserAndWorkData() {
		await refreshData(true, false, true);
	}

	// Success callback handlers for edit operations
	function onUserEditSuccess() {
		editUserDropdownOpen = false;
		refreshUserProfile();
		
		if (selectedUserId) {
			dispatch('userUpdated', { userId: selectedUserId });
		}
	}

	function onWorkEditSuccess() {
		editWorkDropdownOpen = false;
		refreshUserAndWorkData();
		
		if (selectedUserId) {
			dispatch('userUpdated', { userId: selectedUserId });
		}
	}

	// Dropdown management functions
	function toggleEditUserDropdown() {
		editUserDropdownOpen = !editUserDropdownOpen;
		if (editUserDropdownOpen) {
			editWorkDropdownOpen = false;
		}
	}

	function toggleEditWorkDropdown() {
		editWorkDropdownOpen = !editWorkDropdownOpen;
		if (editWorkDropdownOpen) {
			editUserDropdownOpen = false;
		}
	}

	function closeAllDropdowns() {
		editUserDropdownOpen = false;
		editWorkDropdownOpen = false;
	}

	function getUserWorkSchedule(userData: any) {
		if (!userData?.work_schedule?.schedule?.workShift) {
			return null;
		}

		const workShift = userData.work_schedule.schedule.workShift;
		const scheduleDisplay: Record<string, string> = {};

		workShift.forEach((dayData: any) => {
			const dayKey = `day_${dayData.day.toLowerCase()}`;

			if (!dayData.active || !dayData.times || dayData.times.length === 0) {
				scheduleDisplay[dayKey] = 'off';
			} else {
				const timeRanges = dayData.times.map((timeSlot: any) => {
					const startTime = formatTime(timeSlot.start);
					const endTime = formatTime(timeSlot.end);
					return `${startTime} - ${endTime}`;
				});

				scheduleDisplay[dayKey] = timeRanges.join(', ');
			}
		});

		return {
			workShift: workShift,
			scheduleDisplay: scheduleDisplay,
			isBusinessHours: user.work_schedule.same_as_business_hours
		};
	}

	// Lifecycle
	onMount(() => {
		if (typeof window !== 'undefined') {
			window.addEventListener('keydown', handleKeydown);
			window.addEventListener('click', handleOutsideClick);
			return () => {
				window.removeEventListener('keydown', handleKeydown);
				window.removeEventListener('click', handleOutsideClick);
			};
		}
	});

	onDestroy(() => {
		if (typeof window !== 'undefined') {
			window.removeEventListener('keydown', handleKeydown);
			window.removeEventListener('click', handleOutsideClick);
		}
	});

	// Handle clicks outside dropdowns to close them
	function handleOutsideClick(event: MouseEvent) {
		const target = event.target as HTMLElement;

		// Check if click is outside edit user dropdown
		if (editUserDropdownOpen) {
			const editUserButton = document.getElementById('user-detail-sidebar-edit-user-dropdown-button');
			const editUserDropdown = editUserDropdownElement;

			if (editUserButton && !editUserButton.contains(target) &&
				editUserDropdown && !editUserDropdown.contains(target)) {
				editUserDropdownOpen = false;
			}
		}

		// Check if click is outside edit work dropdown
		if (editWorkDropdownOpen) {
			const editWorkButton = document.getElementById('user-detail-sidebar-edit-work-dropdown-button');
			const editWorkDropdown = editWorkDropdownElement;

			if (editWorkButton && !editWorkButton.contains(target) &&
				editWorkDropdown && !editWorkDropdown.contains(target)) {
				editWorkDropdownOpen = false;
			}
		}
	}
</script>

<style>
	:global(.no-cell-padding) {
		padding: 0 !important;
	}
</style>

<!-- Sidebar Container -->
<div
	id="user-detail-sidebar-container"
	class="fixed m-4 rounded-xl inset-y-0 right-0 z-50 w-full md:w-2/5 transform bg-white shadow-xl transition-transform duration-300 ease-in-out {isOpen ? 'translate-x-0' : 'translate-x-full'}"
	role="dialog"
	aria-modal="true"
	aria-labelledby="user-detail-sidebar-title"
>
	<!-- Header -->
	<div id="user-detail-sidebar-header" class="flex items-center justify-between border-b border-gray-200 p-4">
		<h2 id="user-detail-sidebar-title" class="text-lg font-semibold text-gray-900">
			{t('user_details')}
		</h2>
		<Button
			id="user-detail-sidebar-close-button"
			color="none"
			size="sm"
			class="p-2 hover:bg-gray-100"
			on:click={closeSidebar}
		>
			<CloseOutline class="h-5 w-5" />
		</Button>
	</div>

	<!-- Content -->
	<div id="user-detail-sidebar-content" class="flex-1 flex flex-col">
		{#if isLoading}
			<div id="user-detail-sidebar-loading" class="flex h-64 items-center justify-center">
				<LoadingSpinner size="lg" />
			</div>
		{:else if error}
			<div id="user-detail-sidebar-error" class="rounded-lg bg-red-50 p-4 m-4">
				<p class="text-sm text-red-600">{error}</p>
				<Button
					id="user-detail-sidebar-retry-button"
					color="red"
					size="sm"
					class="mt-2"
					on:click={fetchUserDetails}
				>
					{t('retry')}
				</Button>
			</div>
		{:else if displayUser}
			<!-- User Header Section -->
			<div id="user-detail-sidebar-user-header" class="p-4 border-b border-gray-200 flex-shrink-0">
				<div class="flex items-start">
					<div class="relative mr-4">
						<div class="h-16 w-16 overflow-hidden rounded-full bg-gray-100 relative">
							{#if hasUserImage}
								<img src="{displayUser.image_url}" alt="{displayUser.first_name} {displayUser.last_name}" class="h-full w-full object-cover" />
							{:else}
								<div class="h-full w-full flex items-center justify-center bg-gray-300 text-gray-700 font-medium text-xl">
									{displayUser.first_name ? displayUser.first_name[0] : ''}{displayUser.last_name ? displayUser.last_name[0] : ''}
								</div>
							{/if}
						</div>

						<!-- Status indicator -->
						{#if userStatusInfo}
							<div
								class="absolute bottom-0 right-0 h-4 w-4 rounded-full border-2 border-white bg-{userStatusInfo.color}-500"
								aria-label={userStatusInfo.label}
								title={userStatusInfo.label}
							></div>
						{/if}
					</div>
					<div class="flex-1">
						<h3 id="user-detail-sidebar-user-name" class="text-lg font-bold text-gray-900 truncate">
							{displayUser.first_name} {displayUser.last_name} ({displayUser.name})
						</h3>
						<div class="flex items-center gap-2 mt-2">
							{#if displayUser.status === 'online'}
								<Badge color="green">{t('online')}</Badge>
							{:else if displayUser.status === 'away'}
								<Badge color="yellow">{t('away')}</Badge>
							{:else if displayUser.status === 'offline'}
								<Badge color="red">{t('offline')}</Badge>
							{:else}
								<Badge color="red">{t('offline')}</Badge>
							{/if}
							<Badge color="blue">{t((displayUser.roles).toLowerCase())}</Badge>
						</div>
					</div>
				</div>
			</div>

			<!-- Tab Navigation -->
			<div id="user-detail-sidebar-tabs-container" class="bg-white border-b border-gray-200 flex-shrink-0">
				<nav id="user-detail-sidebar-tabs" class="flex w-full">
					{#each TABS as tab}
						<button
							id="user-detail-sidebar-tab-{tab.id}"
							on:click={() => activeTab = tab.id}
							class="flex-1 px-4 py-4 text-sm font-medium border-b-2 transition-colors whitespace-nowrap text-center
								{activeTab === tab.id
									? 'border-black text-black bg-white'
									: 'border-transparent text-gray-500 hover:text-gray-700'}"
							aria-selected={activeTab === tab.id}
							role="tab"
						>
							{t(tab.key)}
						</button>
					{/each}
				</nav>
			</div>

			<!-- Tab Content -->
			<div id="user-detail-sidebar-tab-content" class="flex-1 overflow-y-auto bg-white w-full">
				<div class="w-full h-full">
					{#each TABS as tab}
						{#if activeTab === tab.id}
							<div id="user-detail-sidebar-tab-content-{tab.id}" role="tabpanel" aria-labelledby="user-detail-sidebar-tab-{tab.id}" class="p-4">
								{#if tab.id === 'profile'}
									<!-- Profile Section -->
									<div id="user-detail-sidebar-profile-section" class="mb-6">
										<div class="rounded-lg border border-gray-200 bg-white shadow-sm">
											<div class="border-b border-gray-200 p-4">
												<div class="flex items-center justify-between w-full">
													<div class="flex items-center gap-2">
														<UserCircleSolid class="h-5 w-5 text-blue-600" />
														<h3 class="text-lg font-medium text-gray-700">{t('user_profile')}</h3>
														{#if isLoadingProfile}
															<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
														{/if}
													</div>
													{#if displayUser.roles !== 'System' && canEditUser}
														<div class="relative flex justify-end">
															<Button
																id="user-detail-sidebar-edit-user-dropdown-button"
																color="blue"
																size="sm"
																class="flex items-center"
																on:click={(e) => {
																	e.stopPropagation();
																	toggleEditUserDropdown();
																}}
															>
																<EditOutline class="h-4 w-4 mr-2" />
																{t('user_edit_menu')}
															</Button>
															{#if editUserDropdownOpen}
																<div
																	bind:this={editUserDropdownElement}
																	class="absolute right-0 top-full z-[60] mt-1 w-48 rounded-md border border-gray-200 bg-white py-1 shadow-lg"
																	data-dropdown-id="editUserDropdown"
																>
																	{#if displayUser.is_active}
																		<div class="px-1">
																			<UserEdit
																				user={displayUser}
																				onSuccess={onUserEditSuccess}
																			/>
																		</div>
																	{/if}
																	{#if ROLE_PRIORITY[currentUserRole] > ROLE_PRIORITY[displayUser.roles]}
																		{#if displayUser.is_active}
																			<div class="px-1">
																				<UserDelete
																					user={displayUser}
																				/>
																			</div>
																		{:else}
																			<div class="px-1">
																				<UserReactivate
																					user={displayUser}
																				/>
																			</div>
																		{/if}
																	{/if}
																</div>
															{/if}
														</div>
													{/if}
												</div>
											</div>
											<div class="p-4">
												<div class="grid grid-cols-3 gap-y-4">
													<div class="text-sm text-gray-500 text-left">{t('user_number')}</div>
													<div class="col-span-2 text-sm text-left">{displayUser.id}</div>
													
													<div class="text-sm text-gray-500 text-left">{t('first_name')}</div>
													<div class="col-span-2 text-sm text-left">{displayUser.first_name || '-'}</div>
													
													<div class="text-sm text-gray-500 text-left">{t('last_name')}</div>
													<div class="col-span-2 text-sm text-left">{displayUser.last_name || '-'}</div>
													
													<div class="text-sm text-gray-500 text-left">{t('username')}</div>
													<div class="col-span-2 text-sm text-left">{displayUser.username || '-'}</div>
													
													<div class="text-sm text-gray-500 text-left">{t('email')}</div>
													<div class="col-span-2 text-sm text-left">{displayUser.email}</div>
													
													<div class="text-sm text-gray-500 text-left">{t('last_active')}</div>
													<div class="col-span-2 text-sm text-left">
														{#if displayUser.last_active}
															{displayDate(displayUser.last_active).date} {displayDate(displayUser.last_active).time}
														{:else}
															-
														{/if}
													</div>
													
													<div class="text-sm text-gray-500 text-left">{t('status')}</div>
													<div class="col-span-2 text-sm text-left">{t(displayUser.status)}</div>
													
													<div class="text-sm text-gray-500 text-left">{t('is_active')}</div>
													<div class="col-span-2 text-sm text-left">
														{#if displayUser.is_active}
															<span class="text-green-600 font-medium">{t('enabled')}</span>
														{:else}
															<span class="text-red-600 font-medium">{t('suspended')}</span>
														{/if}
													</div>
												</div>
											</div>
										</div>
									</div>

								{:else if tab.id === 'work'}
									<!-- Work Information Section -->
									<div id="user-detail-sidebar-work-section" class="mb-6">
										<div class="rounded-lg border border-gray-200 bg-white shadow-sm">
											<div class="border-b border-gray-200 p-4">
												<div class="flex items-center justify-between w-full">
													<div class="flex items-center gap-2">
														<BriefcaseSolid class="h-5 w-5 text-blue-600" />
														<h3 class="text-lg font-medium text-gray-700">{t('work_information')}</h3>
														{#if isLoadingWorkData}
															<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
														{/if}
													</div>
													{#if displayUser.roles !== 'System' && canEditUser && displayUser.is_active}
														<div class="relative flex justify-end">
															<Button
																id="user-detail-sidebar-edit-work-dropdown-button"
																color="blue"
																size="sm"
																class="flex items-center"
																on:click={(e) => {
																	e.stopPropagation();
																	toggleEditWorkDropdown();
																}}
															>
																<EditOutline class="h-4 w-4 mr-2" />
																{t('user_edit_menu')}
															</Button>
															{#if editWorkDropdownOpen}
																<div
																	bind:this={editWorkDropdownElement}
																	class="absolute right-0 top-full z-[60] mt-1 w-48 rounded-md border border-gray-200 bg-white py-1 shadow-lg"
																	data-dropdown-id="editWorkDropdown"
																>
																	{#if currentUserRole === 'Admin' && (ROLE_PRIORITY[currentUserRole] > ROLE_PRIORITY[displayUser.roles])}
																		<div class="px-1">
																			<UserAssignRole
																				user={displayUser}
																				roles={roles}
																				onSuccess={onWorkEditSuccess}
																			/>
																		</div>
																	{/if}
																	<div class="px-1">
																		<UserAssignPartner
																			user={displayUser}
																			partners={partners}
																			onSuccess={onWorkEditSuccess}
																		/>
																	</div>
																	<div class="px-1">
																		<UserAssignDepartment
																			user={displayUser}
																			departments={departments}
																			onSuccess={onWorkEditSuccess}
																		/>
																	</div>
																	<div class="px-1">
																		<UserAssignTag
																			user={displayUser}
																			tags={tags}
																			onSuccess={onWorkEditSuccess}
																		/>
																	</div>
																</div>
															{/if}
														</div>
													{/if}
												</div>
											</div>
											<div class="p-4">
												<div class="grid grid-cols-3 gap-y-4">
													<div class="text-sm text-gray-500 text-left">{t('role')}</div>
													<div class="col-span-2 text-sm text-left">{t(displayUser.roles.toLowerCase())}</div>
													
													<div class="text-sm text-gray-500 text-left">{t('current_workload')}</div>
													<div class="col-span-2 text-sm text-left">{userTickets.filter(ticket => ticket.status === 'assigned').length}</div>
													
													<div class="text-sm text-gray-500 text-left">{t('partner')}</div>
													<div class="col-span-2 text-sm text-left">
														{#if displayUser.partners && displayUser.partners.length}
															{#each displayUser.partners as partner}
																<span class="text-white-700 mb-1 mr-1 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm">
																	<Indicator size="sm" class={`mr-1 ${getColorClass(partner.color)} inline-block`} />
																	{partner.name}
																</span>
															{/each}
														{:else}
															{t('no_partners')}
														{/if}
													</div>
													
													<div class="text-sm text-gray-500 text-left">{t('department')}</div>
													<div class="col-span-2 text-sm text-left">
														{#if displayUser.departments && displayUser.departments.length}
															{#each displayUser.departments as department}
																<span class="text-white-700 mb-1 mr-1 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm">
																	<Indicator size="sm" class={`mr-1 ${getColorClass(department.color)} inline-block`} />
																	{department.name}
																</span>
															{/each}
														{:else}
															{t('no_departments')}
														{/if}
													</div>
													
													<div class="text-sm text-gray-500 text-left">{t('specialized_tags')}</div>
													<div class="col-span-2 text-sm text-left">
														{#if displayUser.tags && displayUser.tags.length}
															{#each displayUser.tags as tag}
																<span class="text-white-700 mb-1 mr-1 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm">
																	<Indicator size="sm" class={`mr-1 ${getColorClass(tag.color)} inline-block`} />
																	{tag.name}
																</span>
															{/each}
														{:else}
															{t('no_specialize_tags')}
														{/if}
													</div>
													
													<div class="text-sm text-gray-500 text-left">{t('work_shift_column')}</div>
													<div class="text-sm text-left">
														{#if displayUser.work_schedule}
															{@const workSchedule = getUserWorkSchedule(displayUser)}
															{#if workSchedule}
																<div class="space-y-1 text-sm">
																	{#each Object.entries(workSchedule.scheduleDisplay) as [dayKey, time]}
																		<div class="flex min-w-[200px] flex-col">
																			<div class="flex items-start justify-between">
																				<span class="font-medium text-gray-900 min-w-[80px]">{t(dayKey)}</span>
																				<div class="text-right text-gray-700 flex-1 ml-2">
																					{#if time === 'off'}
																						<span class="text-gray-500">{t('off')}</span>
																					{:else if typeof time === 'string' && time.includes(', ')}
																						{#each time.split(', ') as timeSlot}
																							<div class="leading-tight">
																								{timeSlot}
																							</div>
																						{/each}
																					{:else}
																						{time}
																					{/if}
																				</div>
																			</div>
																		</div>
																	{/each}
																</div>
															{:else}
																{t('no_schedule_set')}
															{/if}
														{:else}
															{t('no_schedule_set')}
														{/if}
													</div>
												</div>
											</div>
										</div>
									</div>

								{:else if tab.id === 'tickets'}
									<!-- Tickets Section -->
									<div id="user-detail-sidebar-tickets-section" class="mb-6">
										<div class="rounded-lg border border-gray-200 bg-white shadow-sm">
											<div class="border-b border-gray-200 p-4">
												<div class="flex items-center gap-2">
													<TicketOutline class="h-5 w-5 text-blue-600" />
													<h3 class="text-lg font-medium text-gray-700">{t('my_tasks')}</h3>
													{#if isLoadingTickets}
														<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
													{/if}
													<span class="rounded-full bg-gray-100 px-2 py-1 text-sm font-medium text-gray-600">
														<!-- {userTickets.filter(ticket => ticket.status === 'assigned').length} -->
														{userTickets.length}
													</span>
												</div>
											</div>
											<div class="w-full">
												{#if userTickets && userTickets.length > 0}
													<div class="h-full max-h-[calc(100vh-24rem)] overflow-y-auto">
														<Table>
															<TableHead>
																<TableHeadCell class="px-2 w-16 text-center">{t('table_no')}</TableHeadCell>
																<TableHeadCell class="px-2 w-16 text-center">{t('table_status')}</TableHeadCell>
																<TableHeadCell class="px-2 w-16 text-center">{t('table_priority')}</TableHeadCell>
																<TableHeadCell class="px-2 w-16 text-center">{t('table_sentiment')}</TableHeadCell>
																<TableHeadCell class="px-2 text-left">{t('table_customer')}</TableHeadCell>
																<TableHeadCell class="px-2 w-32 text-left">{t('table_time')}</TableHeadCell>
																<TableHeadCell class="px-2 w-32 text-left">{t('table_updated_on')}</TableHeadCell>
															</TableHead>
															<TableBody>
																{#each userTickets as ticket}
																	<TableBodyRow>
																		<TableBodyCell class="px-2 w-16 text-center justify-center">
																			<a
																				href="/monitoring/{ticket.id}"
																				class="flex items-center justify-start text-blue-600 hover:underline pl-2"
																			>
																				<EditSolid class="h-4 w-4" /> {ticket.id}
																			</a>
																		</TableBodyCell>
																		<TableBodyCell class="px-2 w-16 text-center justify-center">
																			<div class="flex justify-start">
																				<span class={`${getStatusClass(ticket.status_id)} px-2 rounded-md text-xs w-20 text-center`}>
																					{ticket.status.charAt(0).toUpperCase() + ticket.status.slice(1)}
																				</span>
																			</div>
																		</TableBodyCell>
																		<TableBodyCell class="px-2 w-16 text-center">
																			<div class="flex justify-start">
																				<span class={`${getPriorityClass(ticket.priority.name)} px-2 rounded-md text-xs w-20`}>
																					{ticket.priority.name ?? "-"}
																				</span>
																			</div>
																		</TableBodyCell>
																		<TableBodyCell class="px-2 w-16 text-center">
																			<div class="flex justify-center">
																				<div class={`flex items-center justify-center gap-1 rounded-md ${getSentimentClass(ticket.latest_analysis?.sentiment)}`}>
																					<img
																						src={getSentimentIcon(ticket.latest_analysis?.sentiment)}
																						alt={ticket.latest_analysis?.sentiment}
																						class="w-5 h-5"
																					/>
																					<Tooltip>{ticket.latest_analysis?.sentiment ?? 'Unclassified'}</Tooltip>
																				</div>
																			</div>
																		</TableBodyCell>
																		<TableBodyCell class="px-2 text-left">
																			{ticket.customer.name
																				? ticket.customer.name
																				: ticket.customer.line_user.display_name}
																		</TableBodyCell>
																		<TableBodyCell class="px-2 w-32 text-left">{timeAgo(ticket.updated_on, ticket.status)}</TableBodyCell>
																		<TableBodyCell class="px-2 w-32 text-left">
																			<div>{displayDate(ticket.updated_on).date}</div>
																			<div class="text-xs text-gray-500">{displayDate(ticket.updated_on).time}</div>
																		</TableBodyCell>
																	</TableBodyRow>
																{/each}
															</TableBody>
														</Table>
													</div>
												{:else}
													<div class="p-6 text-center text-gray-500">
														{t('no_tasks_assigned')}
													</div>
												{/if}
											</div>
										</div>
									</div>
								{/if}
							</div>
						{/if}
					{/each}
				</div>
			</div>
		{/if}
	</div>
</div>