<script lang="ts">
	import { enhance } from '$app/forms';
	import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ert, Checkbox, Label } from 'flowbite-svelte';
	import { CheckOutline } from 'flowbite-svelte-icons';
	import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';
	import { t } from '$src/lib/stores/i18n';

	// Expecting the current user and the list of departments as props.
	export let user: any;
	export let departments: any[];
	export let onSuccess: () => void = () => {}; // Callback for successful operations

	let userAssignDepartmentForm: HTMLFormElement;
	let userAssignDepartmentModalOpen = false;
	let currentUser: any = null;
	let selectedDepartmentIds: (string | number)[] = [];
	let initialSelectedDepartmentIds: (string | number)[] = []; // Track initial state for change detection

	let showSuccessMessage = false;
	let showErrorMessage = false;
	let successMessage = '';
	let errorMessage = '';
	let fieldErrors: Record<string, string[]> = {}; // Multi-field error support

	// Function to parse error messages (supports both single string and multi-field errors)
	function parseErrorMessages(error: any): {
		fieldErrors: Record<string, string[]>;
		generalError: string | null;
	} {
		if (!error) return { fieldErrors: {}, generalError: null };

		let errorObj = error;

		// Handle string errors - check if it's a JSON string first
		if (typeof error === 'string') {
			// Try to parse as JSON first
			try {
				errorObj = JSON.parse(error);
			} catch (e) {
				// If JSON parsing fails, treat as plain string (backward compatibility)
				return { fieldErrors: {}, generalError: error };
			}
		}

		// Handle object errors with field-specific messages
		if (typeof errorObj === 'object' && errorObj !== null) {
			const fieldErrors: Record<string, string[]> = {};
			let hasFieldErrors = false;

			for (const [fieldName, fieldErrorArray] of Object.entries(errorObj)) {
				if (Array.isArray(fieldErrorArray)) {
					const validErrors = fieldErrorArray.filter((msg: any) => typeof msg === 'string');
					if (validErrors.length > 0) {
						fieldErrors[fieldName] = validErrors;
						hasFieldErrors = true;
					}
				}
			}

			if (hasFieldErrors) {
				return { fieldErrors, generalError: null };
			}
		}

		return { fieldErrors: {}, generalError: 'An error occurred' };
	}

	// Function to dismiss alerts when selection changes
	function dismissAlerts() {
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
		fieldErrors = {};
	}

	// Reactive statement to detect changes from initial state
	$: hasChanges =
		JSON.stringify([...selectedDepartmentIds].sort()) !==
		JSON.stringify([...initialSelectedDepartmentIds].sort());

	// Open the modal and initialize with the current user's departments if available.
	function openUserAssignDepartmentModal(user: any) {
		currentUser = { ...user };

		if (currentUser.departments && Array.isArray(currentUser.departments)) {
			selectedDepartmentIds = currentUser.departments
				.map((dept: any) => (typeof dept === 'object' ? dept.id : dept))
				// Only keep valid number values.
				.filter((id: any) => id !== undefined && id !== null && !isNaN(Number(id)));
		} else {
			selectedDepartmentIds = [];
		}

		// Store initial state for change detection
		initialSelectedDepartmentIds = [...selectedDepartmentIds];

		userAssignDepartmentModalOpen = true;
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
		fieldErrors = {}; // Reset field errors
	}

	// Form submission handler.
	function handleUserAssignDepartmentSubmit(_event: Event) {
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
	}

	// Options for handling form enhancement.
	$: enhanceOptions = {
		modalOpen: userAssignDepartmentModalOpen,
		setModalOpen: (value: boolean) => (userAssignDepartmentModalOpen = value),
		setShowSuccessMessage: (value: boolean) => (showSuccessMessage = value),
		setSuccessMessage: (value: string) => (successMessage = value),
		successMessage: t('user_assign_department_success'),
		setShowErrorMessage: (value: boolean) => (showErrorMessage = value),
		setErrorMessage: (value: string) => {
			const parsedResult = parseErrorMessages(value);
			fieldErrors = parsedResult.fieldErrors;
			errorMessage = parsedResult.generalError || '';
			// Set showErrorMessage to true when we have either field errors or general error
			showErrorMessage =
				Object.keys(parsedResult.fieldErrors).length > 0 || parsedResult.generalError !== null;
		},
		// Enhanced success behavior - close modal and show toast
		useToastOnSuccess: true,
		closeModalOnSuccess: true,
		onSuccess: () => {
			// Call the original onSuccess if provided
			if (onSuccess) onSuccess();
		}
	};

	// Map departments into the format expected by the checkbox component.
	$: departmentOptions = departments.map((department) => ({
		value: department.id,
		name: `${department.name} (${department.code || 'N/A'})`
	}));
</script>

<!-- Button to open the assign department modal -->
<Button
	color="none"
	class="w-full justify-start p-2 text-left hover:bg-gray-100"
	on:click={() => openUserAssignDepartmentModal(user)}
>
	{t('user_assign_department')}
</Button>

<!-- Modal for assigning departments -->
<Modal bind:open={userAssignDepartmentModalOpen} size="md" autoclose={false} class="w-full">
	<h2 slot="header">{t('user_assign_department')}</h2>
	{#if currentUser}
		{#if showSuccessMessage}
			<Alert color="green" class="mb-4">
				{successMessage}
			</Alert>
		{/if}
		{#if showErrorMessage}
			<Alert color="red" class="mb-4">
				{errorMessage}
			</Alert>
		{/if}
		<!-- Field-specific error display for multi-field errors -->
		{#if Object.keys(fieldErrors).length > 0}
			{#each Object.entries(fieldErrors) as [fieldName, errors]}
				{#each errors as error}
					<Alert color="red" class="mb-4">
						<strong>{fieldName}:</strong>
						{error}
					</Alert>
				{/each}
			{/each}
		{/if}
		<form
			bind:this={userAssignDepartmentForm}
			action="?/assign_user_department"
			method="POST"
			use:enhance={() => handleEnhance(enhanceOptions)}
			on:submit={handleUserAssignDepartmentSubmit}
		>
			<!-- Hidden input for the user id -->
			<input type="hidden" name="id" value={currentUser.id} />
			<div class="min-h-[200px]">
				<!-- <label
					for="SelectedDepartments"
					class="mb-1 block text-left text-sm font-medium text-gray-700"
				>
					Select User's Departments
				</label> -->
				{#each departmentOptions as department (department.value)}
					<div>
						<Label class="flex items-center">
							<Checkbox
								inline
								class="p-2 text-gray-700 focus:ring-gray-500 focus:border-gray-500"
								checked={selectedDepartmentIds.includes(department.value)}
								value={department.value}
								on:change={() => {
									if (selectedDepartmentIds.includes(department.value)) {
										selectedDepartmentIds = selectedDepartmentIds.filter(
											(id) => id !== department.value
										);
									} else {
										selectedDepartmentIds = [...selectedDepartmentIds, department.value];
									}
									dismissAlerts(); // Dismiss alerts when checkbox selection changes
								}}
							/>
							{department.name}
						</Label>
					</div>
				{/each}
				<!-- Hidden input to submit the selected department IDs -->
				<input type="hidden" name="department_ids[]" value={selectedDepartmentIds} />
			</div>
		</form>
	{/if}
	<svelte:fragment slot="footer">
		<Button
			color="green"
			disabled={!hasChanges}
			on:click={() => userAssignDepartmentForm.requestSubmit()}
			><CheckOutline class="mr-2 h-4 w-4" />
			{t('save')}</Button
		>
		<Button color="light" on:click={() => (userAssignDepartmentModalOpen = false)}
			>{t('cancel')}</Button
		>
	</svelte:fragment>
</Modal>
